
[root@192 ~]# docker ps -a | grep nacos
ec96e21fd51f   nacos/nacos-server:v2.5.0           "sh bin/docker-start…"   23 hours ago   Up 21 minutes   0.0.0.0:8848->8848/tcp, [::]:8848->8848/tcp, 0.0.0.0:9848-9849->9848-9849/tcp, [::]:9848-9849->9848-9849/tcp                         nacos
[root@192 ~]# docker logs nacos
+ export 'CUSTOM_SEARCH_NAMES=application'
+ export 'CUSTOM_SEARCH_LOCATIONS=file:/home/<USER>/conf/'
+ export 'MEMBER_LIST='
+ PLUGINS_DIR=/home/<USER>/plugins/peer-finder
+ join_if_exist -Xms 1g
+ '[' -n 1g ]
+ echo -Xms1g
+ Xms=-Xms1g
+ join_if_exist -Xmx 1g
+ '[' -n 1g ]
+ echo -Xmx1g
+ Xmx=-Xmx1g
+ join_if_exist -Xmn 512m
+ '[' -n 512m ]
+ echo -Xmn512m
+ Xmn=-Xmn512m
+ join_if_exist '-XX:MetaspaceSize=' 128m
+ '[' -n 128m ]
+ echo '-XX:MetaspaceSize=128m'
+ XX_MS='-XX:MetaspaceSize=128m'
+ join_if_exist '-XX:MaxMetaspaceSize=' 320m
+ '[' -n 320m ]
+ echo '-XX:MaxMetaspaceSize=320m'
+ XX_MMS='-XX:MaxMetaspaceSize=320m'
+ JAVA_OPT=' -XX:+UseConcMarkSweepGC -XX:+UseCMSCompactAtFullCollection -XX:CMSInitiatingOccupancyFraction=70 -XX:+CMSParallelRemarkEnabled -XX:SoftRefLRUPolicyMSPerMB=0 -XX:+CMSClassUnloadingEnabled -XX:SurvivorRatio=8 '
+ '[[' standalone '==' standalone ]]
+ JAVA_OPT=' -XX:+UseConcMarkSweepGC -XX:+UseCMSCompactAtFullCollection -XX:CMSInitiatingOccupancyFraction=70 -XX:+CMSParallelRemarkEnabled -XX:SoftRefLRUPolicyMSPerMB=0 -XX:+CMSClassUnloadingEnabled -XX:SurvivorRatio=8  -Xms1g -Xmx1g -Xmn512m'
+ JAVA_OPT=' -XX:+UseConcMarkSweepGC -XX:+UseCMSCompactAtFullCollection -XX:CMSInitiatingOccupancyFraction=70 -XX:+CMSParallelRemarkEnabled -XX:SoftRefLRUPolicyMSPerMB=0 -XX:+CMSClassUnloadingEnabled -XX:SurvivorRatio=8  -Xms1g -Xmx1g -Xmn512m -Dnacos.standalone=true'
+ '[[' all '==' config ]]
+ '[[' all '==' naming ]]
+ '[[' '!' -z  ]]
+ '[[' '!' -z  ]]
+ '[[' '!' -z  ]]
+ '[[' '!' -z  ]]
+ '[[' '!' -z true ]]
+ JAVA_OPT=' -XX:+UseConcMarkSweepGC -XX:+UseCMSCompactAtFullCollection -XX:CMSInitiatingOccupancyFraction=70 -XX:+CMSParallelRemarkEnabled -XX:SoftRefLRUPolicyMSPerMB=0 -XX:+CMSClassUnloadingEnabled -XX:SurvivorRatio=8  -Xms1g -Xmx1g -Xmn512m -Dnacos.standalone=true -Dnacos.core.auth.enabled=true'
+ '[[' ip '==' hostname ]]
+ JAVA_OPT=' -XX:+UseConcMarkSweepGC -XX:+UseCMSCompactAtFullCollection -XX:CMSInitiatingOccupancyFraction=70 -XX:+CMSParallelRemarkEnabled -XX:SoftRefLRUPolicyMSPerMB=0 -XX:+CMSClassUnloadingEnabled -XX:SurvivorRatio=8  -Xms1g -Xmx1g -Xmn512m -Dnacos.standalone=true -Dnacos.core.auth.enabled=true -Dnacos.member.list='
+ /usr/lib/jvm/java-1.8-openjdk/bin/java -version
+ sed -E -n 's/.* version "([0-9]*).*$/\1/p'
+ JAVA_MAJOR_VERSION=1
+ '[[' 1 -ge 9 ]]
+ JAVA_OPT_EXT_FIX='-Djava.ext.dirs=/usr/lib/jvm/java-1.8-openjdk/jre/lib/ext:/usr/lib/jvm/java-1.8-openjdk/lib/ext'
+ JAVA_OPT=' -XX:+UseConcMarkSweepGC -XX:+UseCMSCompactAtFullCollection -XX:CMSInitiatingOccupancyFraction=70 -XX:+CMSParallelRemarkEnabled -XX:SoftRefLRUPolicyMSPerMB=0 -XX:+CMSClassUnloadingEnabled -XX:SurvivorRatio=8  -Xms1g -Xmx1g -Xmn512m -Dnacos.standalone=true -Dnacos.core.auth.enabled=true -Dnacos.member.list= -Xloggc:/home/<USER>/logs/nacos_gc.log -verbose:gc -XX:+PrintGCDetails -XX:+PrintGCDateStamps -XX:+PrintGCTimeStamps -XX:+UseGCLogFileRotation -XX:NumberOfGCLogFiles=10 -XX:GCLogFileSize=100M'
+ JAVA_OPT=' -XX:+UseConcMarkSweepGC -XX:+UseCMSCompactAtFullCollection -XX:CMSInitiatingOccupancyFraction=70 -XX:+CMSParallelRemarkEnabled -XX:SoftRefLRUPolicyMSPerMB=0 -XX:+CMSClassUnloadingEnabled -XX:SurvivorRatio=8  -Xms1g -Xmx1g -Xmn512m -Dnacos.standalone=true -Dnacos.core.auth.enabled=true -Dnacos.member.list= -Xloggc:/home/<USER>/logs/nacos_gc.log -verbose:gc -XX:+PrintGCDetails -XX:+PrintGCDateStamps -XX:+PrintGCTimeStamps -XX:+UseGCLogFileRotation -XX:NumberOfGCLogFiles=10 -XX:GCLogFileSize=100M -Dloader.path=/home/<USER>/plugins,/home/<USER>/plugins/health,/home/<USER>/plugins/cmdb,/home/<USER>/plugins/selector'
+ JAVA_OPT=' -XX:+UseConcMarkSweepGC -XX:+UseCMSCompactAtFullCollection -XX:CMSInitiatingOccupancyFraction=70 -XX:+CMSParallelRemarkEnabled -XX:SoftRefLRUPolicyMSPerMB=0 -XX:+CMSClassUnloadingEnabled -XX:SurvivorRatio=8  -Xms1g -Xmx1g -Xmn512m -Dnacos.standalone=true -Dnacos.core.auth.enabled=true -Dnacos.member.list= -Xloggc:/home/<USER>/logs/nacos_gc.log -verbose:gc -XX:+PrintGCDetails -XX:+PrintGCDateStamps -XX:+PrintGCTimeStamps -XX:+UseGCLogFileRotation -XX:NumberOfGCLogFiles=10 -XX:GCLogFileSize=100M -Dloader.path=/home/<USER>/plugins,/home/<USER>/plugins/health,/home/<USER>/plugins/cmdb,/home/<USER>/plugins/selector -Dnacos.home=/home/<USER>'
+ JAVA_OPT=' -XX:+UseConcMarkSweepGC -XX:+UseCMSCompactAtFullCollection -XX:CMSInitiatingOccupancyFraction=70 -XX:+CMSParallelRemarkEnabled -XX:SoftRefLRUPolicyMSPerMB=0 -XX:+CMSClassUnloadingEnabled -XX:SurvivorRatio=8  -Xms1g -Xmx1g -Xmn512m -Dnacos.standalone=true -Dnacos.core.auth.enabled=true -Dnacos.member.list= -Xloggc:/home/<USER>/logs/nacos_gc.log -verbose:gc -XX:+PrintGCDetails -XX:+PrintGCDateStamps -XX:+PrintGCTimeStamps -XX:+UseGCLogFileRotation -XX:NumberOfGCLogFiles=10 -XX:GCLogFileSize=100M -Dloader.path=/home/<USER>/plugins,/home/<USER>/plugins/health,/home/<USER>/plugins/cmdb,/home/<USER>/plugins/selector -Dnacos.home=/home/<USER>/home/<USER>/target/nacos-server.jar'
+ JAVA_OPT=' -XX:+UseConcMarkSweepGC -XX:+UseCMSCompactAtFullCollection -XX:CMSInitiatingOccupancyFraction=70 -XX:+CMSParallelRemarkEnabled -XX:SoftRefLRUPolicyMSPerMB=0 -XX:+CMSClassUnloadingEnabled -XX:SurvivorRatio=8  -Xms1g -Xmx1g -Xmn512m -Dnacos.standalone=true -Dnacos.core.auth.enabled=true -Dnacos.member.list= -Xloggc:/home/<USER>/logs/nacos_gc.log -verbose:gc -XX:+PrintGCDetails -XX:+PrintGCDateStamps -XX:+PrintGCTimeStamps -XX:+UseGCLogFileRotation -XX:NumberOfGCLogFiles=10 -XX:GCLogFileSize=100M -Dloader.path=/home/<USER>/plugins,/home/<USER>/plugins/health,/home/<USER>/plugins/cmdb,/home/<USER>/plugins/selector -Dnacos.home=/home/<USER>/home/<USER>/target/nacos-server.jar '
Nacos is starting, you can docker logs your container
+ JAVA_OPT=' -XX:+UseConcMarkSweepGC -XX:+UseCMSCompactAtFullCollection -XX:CMSInitiatingOccupancyFraction=70 -XX:+CMSParallelRemarkEnabled -XX:SoftRefLRUPolicyMSPerMB=0 -XX:+CMSClassUnloadingEnabled -XX:SurvivorRatio=8  -Xms1g -Xmx1g -Xmn512m -Dnacos.standalone=true -Dnacos.core.auth.enabled=true -Dnacos.member.list= -Xloggc:/home/<USER>/logs/nacos_gc.log -verbose:gc -XX:+PrintGCDetails -XX:+PrintGCDateStamps -XX:+PrintGCTimeStamps -XX:+UseGCLogFileRotation -XX:NumberOfGCLogFiles=10 -XX:GCLogFileSize=100M -Dloader.path=/home/<USER>/plugins,/home/<USER>/plugins/health,/home/<USER>/plugins/cmdb,/home/<USER>/plugins/selector -Dnacos.home=/home/<USER>/home/<USER>/target/nacos-server.jar  --spring.config.additional-location=file:/home/<USER>/conf/'
+ JAVA_OPT=' -XX:+UseConcMarkSweepGC -XX:+UseCMSCompactAtFullCollection -XX:CMSInitiatingOccupancyFraction=70 -XX:+CMSParallelRemarkEnabled -XX:SoftRefLRUPolicyMSPerMB=0 -XX:+CMSClassUnloadingEnabled -XX:SurvivorRatio=8  -Xms1g -Xmx1g -Xmn512m -Dnacos.standalone=true -Dnacos.core.auth.enabled=true -Dnacos.member.list= -Xloggc:/home/<USER>/logs/nacos_gc.log -verbose:gc -XX:+PrintGCDetails -XX:+PrintGCDateStamps -XX:+PrintGCTimeStamps -XX:+UseGCLogFileRotation -XX:NumberOfGCLogFiles=10 -XX:GCLogFileSize=100M -Dloader.path=/home/<USER>/plugins,/home/<USER>/plugins/health,/home/<USER>/plugins/cmdb,/home/<USER>/plugins/selector -Dnacos.home=/home/<USER>/home/<USER>/target/nacos-server.jar  --spring.config.additional-location=file:/home/<USER>/conf/ --spring.config.name=application'
+ JAVA_OPT=' -XX:+UseConcMarkSweepGC -XX:+UseCMSCompactAtFullCollection -XX:CMSInitiatingOccupancyFraction=70 -XX:+CMSParallelRemarkEnabled -XX:SoftRefLRUPolicyMSPerMB=0 -XX:+CMSClassUnloadingEnabled -XX:SurvivorRatio=8  -Xms1g -Xmx1g -Xmn512m -Dnacos.standalone=true -Dnacos.core.auth.enabled=true -Dnacos.member.list= -Xloggc:/home/<USER>/logs/nacos_gc.log -verbose:gc -XX:+PrintGCDetails -XX:+PrintGCDateStamps -XX:+PrintGCTimeStamps -XX:+UseGCLogFileRotation -XX:NumberOfGCLogFiles=10 -XX:GCLogFileSize=100M -Dloader.path=/home/<USER>/plugins,/home/<USER>/plugins/health,/home/<USER>/plugins/cmdb,/home/<USER>/plugins/selector -Dnacos.home=/home/<USER>/home/<USER>/target/nacos-server.jar  --spring.config.additional-location=file:/home/<USER>/conf/ --spring.config.name=application --logging.config=/home/<USER>/conf/nacos-logback.xml'
+ JAVA_OPT=' -XX:+UseConcMarkSweepGC -XX:+UseCMSCompactAtFullCollection -XX:CMSInitiatingOccupancyFraction=70 -XX:+CMSParallelRemarkEnabled -XX:SoftRefLRUPolicyMSPerMB=0 -XX:+CMSClassUnloadingEnabled -XX:SurvivorRatio=8  -Xms1g -Xmx1g -Xmn512m -Dnacos.standalone=true -Dnacos.core.auth.enabled=true -Dnacos.member.list= -Xloggc:/home/<USER>/logs/nacos_gc.log -verbose:gc -XX:+PrintGCDetails -XX:+PrintGCDateStamps -XX:+PrintGCTimeStamps -XX:+UseGCLogFileRotation -XX:NumberOfGCLogFiles=10 -XX:GCLogFileSize=100M -Dloader.path=/home/<USER>/plugins,/home/<USER>/plugins/health,/home/<USER>/plugins/cmdb,/home/<USER>/plugins/selector -Dnacos.home=/home/<USER>/home/<USER>/target/nacos-server.jar  --spring.config.additional-location=file:/home/<USER>/conf/ --spring.config.name=application --logging.config=/home/<USER>/conf/nacos-logback.xml --server.max-http-header-size=524288'
+ echo 'Nacos is starting, you can docker logs your container'
+ exec /usr/lib/jvm/java-1.8-openjdk/bin/java -XX:+UseConcMarkSweepGC -XX:+UseCMSCompactAtFullCollection '-XX:CMSInitiatingOccupancyFraction=70' -XX:+CMSParallelRemarkEnabled '-XX:SoftRefLRUPolicyMSPerMB=0' -XX:+CMSClassUnloadingEnabled '-XX:SurvivorRatio=8' -Xms1g -Xmx1g -Xmn512m '-Dnacos.standalone=true' '-Dnacos.core.auth.enabled=true' '-Dnacos.member.list=' -Xloggc:/home/<USER>/logs/nacos_gc.log -verbose:gc -XX:+PrintGCDetails -XX:+PrintGCDateStamps -XX:+PrintGCTimeStamps -XX:+UseGCLogFileRotation '-XX:NumberOfGCLogFiles=10' '-XX:GCLogFileSize=100M' '-Dloader.path=/home/<USER>/plugins,/home/<USER>/plugins/health,/home/<USER>/plugins/cmdb,/home/<USER>/plugins/selector' '-Dnacos.home=/home/<USER>' -jar /home/<USER>/target/nacos-server.jar '--spring.config.additional-location=file:/home/<USER>/conf/' '--spring.config.name=application' '--logging.config=/home/<USER>/conf/nacos-logback.xml' '--server.max-http-header-size=524288'
OpenJDK 64-Bit Server VM warning: UseCMSCompactAtFullCollection is deprecated and will likely be removed in a future release.

         ,--.
       ,--.'|
   ,--,:  : |                                           Nacos 2.5.0
,`--.'`|  ' :                       ,---.               Running in stand alone mode, All function modules
|   :  :  | |                      '   ,'\   .--.--.    Port: 8848
:   |   \ | :  ,--.--.     ,---.  /   /   | /  /    '   Pid: 1
|   : '  '; | /       \   /     \.   ; ,. :|  :  /`./   Console: http://192.168.91.128:8848/nacos/index.html
'   ' ;.    ;.--.  .-. | /    / ''   | |: :|  :  ;_
|   | | \   | \__\/: . ..    ' / '   | .; : \  \    `.      https://nacos.io
'   : |  ; .' ," .--.; |'   ; :__|   :    |  `----.   \
|   | '`--'  /  /  ,.  |'   | '.'|\   \  /  /  /`--'  /
'   : |     ;  :   .'   \   :    : `----'  '--'.     /
;   |.'     |  ,     .-./\   \  /            `--'---'
'---'        `--`---'     `----'

2025-07-29 15:29:39,336 INFO Tomcat initialized with port(s): 8848 (http)

2025-07-29 15:29:39,617 INFO Root WebApplicationContext: initialization completed in 10381 ms

2025-07-29 15:29:51,729 INFO Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@2e2f720, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@3ae2ed38, org.springframework.security.web.context.SecurityContextPersistenceFilter@4ce14f05, org.springframework.security.web.header.HeaderWriterFilter@31133b6e, org.springframework.security.web.csrf.CsrfFilter@43034809, org.springframework.security.web.authentication.logout.LogoutFilter@1ffcf674, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@1b7cae6f, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@26a262d6, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@7f572c37, org.springframework.security.web.session.SessionManagementFilter@7ea08277, org.springframework.security.web.access.ExceptionTranslationFilter@784c5ef5]

2025-07-29 15:29:52,711 INFO Adding welcome page: class path resource [static/index.html]

2025-07-29 15:29:54,052 INFO Exposing 14 endpoint(s) beneath base path '/actuator'

2025-07-29 15:29:54,135 WARN You are asking Spring Security to ignore Ant [pattern='/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.

2025-07-29 15:29:54,135 INFO Will not secure Ant [pattern='/**']

2025-07-29 15:29:54,135 WARN You are asking Spring Security to ignore Mvc [pattern='/prometheus']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.

2025-07-29 15:29:54,136 INFO Will not secure Mvc [pattern='/prometheus']

2025-07-29 15:29:54,136 WARN You are asking Spring Security to ignore Mvc [pattern='/prometheus/namespaceId/{namespaceId}']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.

2025-07-29 15:29:54,136 INFO Will not secure Mvc [pattern='/prometheus/namespaceId/{namespaceId}']

2025-07-29 15:29:54,136 WARN You are asking Spring Security to ignore Mvc [pattern='/prometheus/namespaceId/{namespaceId}/service/{service}']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.

2025-07-29 15:29:54,136 INFO Will not secure Mvc [pattern='/prometheus/namespaceId/{namespaceId}/service/{service}']

2025-07-29 15:29:54,348 INFO Tomcat started on port(s): 8848 (http) with context path '/nacos'

2025-07-29 15:29:54,424 INFO No TaskScheduler/ScheduledExecutorService bean found for scheduled processing

2025-07-29 15:29:54,522 INFO Nacos started successfully in stand alone mode. use external storage

2025-07-29 15:29:59,435 INFO Initializing Servlet 'dispatcherServlet'

2025-07-29 15:29:59,437 INFO Completed initialization in 2 ms

2025-07-30 13:59:55,521 INFO [NotifyCenter] Start destroying Publisher

2025-07-30 13:59:55,522 INFO [NotifyCenter] Completed destruction of Publisher

2025-07-30 13:59:55,523 WARN [WatchFileCenter] start close

2025-07-30 13:59:55,525 WARN [WatchFileCenter] start to shutdown this watcher which is watch : /home/<USER>/conf

2025-07-30 13:59:55,526 INFO [ThreadPoolManager] Start destroying ThreadPool

2025-07-30 13:59:55,533 WARN [WatchFileCenter] already closed

+ export 'CUSTOM_SEARCH_NAMES=application'
+ export 'CUSTOM_SEARCH_LOCATIONS=file:/home/<USER>/conf/'
+ export 'MEMBER_LIST='
+ PLUGINS_DIR=/home/<USER>/plugins/peer-finder
+ join_if_exist -Xms 1g
+ '[' -n 1g ]
+ echo -Xms1g
+ Xms=-Xms1g
+ join_if_exist -Xmx 1g
+ '[' -n 1g ]
+ echo -Xmx1g
+ Xmx=-Xmx1g
+ join_if_exist -Xmn 512m
+ '[' -n 512m ]
+ echo -Xmn512m
+ Xmn=-Xmn512m
+ join_if_exist '-XX:MetaspaceSize=' 128m
+ '[' -n 128m ]
+ echo '-XX:MetaspaceSize=128m'
+ XX_MS='-XX:MetaspaceSize=128m'
+ join_if_exist '-XX:MaxMetaspaceSize=' 320m
+ '[' -n 320m ]
+ echo '-XX:MaxMetaspaceSize=320m'
+ XX_MMS='-XX:MaxMetaspaceSize=320m'
+ JAVA_OPT=' -XX:+UseConcMarkSweepGC -XX:+UseCMSCompactAtFullCollection -XX:CMSInitiatingOccupancyFraction=70 -XX:+CMSParallelRemarkEnabled -XX:SoftRefLRUPolicyMSPerMB=0 -XX:+CMSClassUnloadingEnabled -XX:SurvivorRatio=8 '
+ '[[' standalone '==' standalone ]]
+ JAVA_OPT=' -XX:+UseConcMarkSweepGC -XX:+UseCMSCompactAtFullCollection -XX:CMSInitiatingOccupancyFraction=70 -XX:+CMSParallelRemarkEnabled -XX:SoftRefLRUPolicyMSPerMB=0 -XX:+CMSClassUnloadingEnabled -XX:SurvivorRatio=8  -Xms1g -Xmx1g -Xmn512m'
+ JAVA_OPT=' -XX:+UseConcMarkSweepGC -XX:+UseCMSCompactAtFullCollection -XX:CMSInitiatingOccupancyFraction=70 -XX:+CMSParallelRemarkEnabled -XX:SoftRefLRUPolicyMSPerMB=0 -XX:+CMSClassUnloadingEnabled -XX:SurvivorRatio=8  -Xms1g -Xmx1g -Xmn512m -Dnacos.standalone=true'
+ '[[' all '==' config ]]
+ '[[' all '==' naming ]]
+ '[[' '!' -z  ]]
+ '[[' '!' -z  ]]
+ '[[' '!' -z  ]]
+ '[[' '!' -z  ]]
+ '[[' '!' -z true ]]
+ JAVA_OPT=' -XX:+UseConcMarkSweepGC -XX:+UseCMSCompactAtFullCollection -XX:CMSInitiatingOccupancyFraction=70 -XX:+CMSParallelRemarkEnabled -XX:SoftRefLRUPolicyMSPerMB=0 -XX:+CMSClassUnloadingEnabled -XX:SurvivorRatio=8  -Xms1g -Xmx1g -Xmn512m -Dnacos.standalone=true -Dnacos.core.auth.enabled=true'
+ '[[' ip '==' hostname ]]
+ JAVA_OPT=' -XX:+UseConcMarkSweepGC -XX:+UseCMSCompactAtFullCollection -XX:CMSInitiatingOccupancyFraction=70 -XX:+CMSParallelRemarkEnabled -XX:SoftRefLRUPolicyMSPerMB=0 -XX:+CMSClassUnloadingEnabled -XX:SurvivorRatio=8  -Xms1g -Xmx1g -Xmn512m -Dnacos.standalone=true -Dnacos.core.auth.enabled=true -Dnacos.member.list='
+ /usr/lib/jvm/java-1.8-openjdk/bin/java -version
+ sed -E -n 's/.* version "([0-9]*).*$/\1/p'
+ JAVA_MAJOR_VERSION=1
+ '[[' 1 -ge 9 ]]
+ JAVA_OPT_EXT_FIX='-Djava.ext.dirs=/usr/lib/jvm/java-1.8-openjdk/jre/lib/ext:/usr/lib/jvm/java-1.8-openjdk/lib/ext'
+ JAVA_OPT=' -XX:+UseConcMarkSweepGC -XX:+UseCMSCompactAtFullCollection -XX:CMSInitiatingOccupancyFraction=70 -XX:+CMSParallelRemarkEnabled -XX:SoftRefLRUPolicyMSPerMB=0 -XX:+CMSClassUnloadingEnabled -XX:SurvivorRatio=8  -Xms1g -Xmx1g -Xmn512m -Dnacos.standalone=true -Dnacos.core.auth.enabled=true -Dnacos.member.list= -Xloggc:/home/<USER>/logs/nacos_gc.log -verbose:gc -XX:+PrintGCDetails -XX:+PrintGCDateStamps -XX:+PrintGCTimeStamps -XX:+UseGCLogFileRotation -XX:NumberOfGCLogFiles=10 -XX:GCLogFileSize=100M'
+ JAVA_OPT=' -XX:+UseConcMarkSweepGC -XX:+UseCMSCompactAtFullCollection -XX:CMSInitiatingOccupancyFraction=70 -XX:+CMSParallelRemarkEnabled -XX:SoftRefLRUPolicyMSPerMB=0 -XX:+CMSClassUnloadingEnabled -XX:SurvivorRatio=8  -Xms1g -Xmx1g -Xmn512m -Dnacos.standalone=true -Dnacos.core.auth.enabled=true -Dnacos.member.list= -Xloggc:/home/<USER>/logs/nacos_gc.log -verbose:gc -XX:+PrintGCDetails -XX:+PrintGCDateStamps -XX:+PrintGCTimeStamps -XX:+UseGCLogFileRotation -XX:NumberOfGCLogFiles=10 -XX:GCLogFileSize=100M -Dloader.path=/home/<USER>/plugins,/home/<USER>/plugins/health,/home/<USER>/plugins/cmdb,/home/<USER>/plugins/selector'
+ JAVA_OPT=' -XX:+UseConcMarkSweepGC -XX:+UseCMSCompactAtFullCollection -XX:CMSInitiatingOccupancyFraction=70 -XX:+CMSParallelRemarkEnabled -XX:SoftRefLRUPolicyMSPerMB=0 -XX:+CMSClassUnloadingEnabled -XX:SurvivorRatio=8  -Xms1g -Xmx1g -Xmn512m -Dnacos.standalone=true -Dnacos.core.auth.enabled=true -Dnacos.member.list= -Xloggc:/home/<USER>/logs/nacos_gc.log -verbose:gc -XX:+PrintGCDetails -XX:+PrintGCDateStamps -XX:+PrintGCTimeStamps -XX:+UseGCLogFileRotation -XX:NumberOfGCLogFiles=10 -XX:GCLogFileSize=100M -Dloader.path=/home/<USER>/plugins,/home/<USER>/plugins/health,/home/<USER>/plugins/cmdb,/home/<USER>/plugins/selector -Dnacos.home=/home/<USER>'
+ JAVA_OPT=' -XX:+UseConcMarkSweepGC -XX:+UseCMSCompactAtFullCollection -XX:CMSInitiatingOccupancyFraction=70 -XX:+CMSParallelRemarkEnabled -XX:SoftRefLRUPolicyMSPerMB=0 -XX:+CMSClassUnloadingEnabled -XX:SurvivorRatio=8  -Xms1g -Xmx1g -Xmn512m -Dnacos.standalone=true -Dnacos.core.auth.enabled=true -Dnacos.member.list= -Xloggc:/home/<USER>/logs/nacos_gc.log -verbose:gc -XX:+PrintGCDetails -XX:+PrintGCDateStamps -XX:+PrintGCTimeStamps -XX:+UseGCLogFileRotation -XX:NumberOfGCLogFiles=10 -XX:GCLogFileSize=100M -Dloader.path=/home/<USER>/plugins,/home/<USER>/plugins/health,/home/<USER>/plugins/cmdb,/home/<USER>/plugins/selector -Dnacos.home=/home/<USER>/home/<USER>/target/nacos-server.jar'
+ JAVA_OPT=' -XX:+UseConcMarkSweepGC -XX:+UseCMSCompactAtFullCollection -XX:CMSInitiatingOccupancyFraction=70 -XX:+CMSParallelRemarkEnabled -XX:SoftRefLRUPolicyMSPerMB=0 -XX:+CMSClassUnloadingEnabled -XX:SurvivorRatio=8  -Xms1g -Xmx1g -Xmn512m -Dnacos.standalone=true -Dnacos.core.auth.enabled=true -Dnacos.member.list= -Xloggc:/home/<USER>/logs/nacos_gc.log -verbose:gc -XX:+PrintGCDetails -XX:+PrintGCDateStamps -XX:+PrintGCTimeStamps -XX:+UseGCLogFileRotation -XX:NumberOfGCLogFiles=10 -XX:GCLogFileSize=100M -Dloader.path=/home/<USER>/plugins,/home/<USER>/plugins/health,/home/<USER>/plugins/cmdb,/home/<USER>/plugins/selector -Dnacos.home=/home/<USER>/home/<USER>/target/nacos-server.jar '
+ JAVA_OPT=' -XX:+UseConcMarkSweepGC -XX:+UseCMSCompactAtFullCollection -XX:CMSInitiatingOccupancyFraction=70 -XX:+CMSParallelRemarkEnabled -XX:SoftRefLRUPolicyMSPerMB=0 -XX:+CMSClassUnloadingEnabled -XX:SurvivorRatio=8  -Xms1g -Xmx1g -Xmn512m -Dnacos.standalone=true -Dnacos.core.auth.enabled=true -Dnacos.member.list= -Xloggc:/home/<USER>/logs/nacos_gc.log -verbose:gc -XX:+PrintGCDetails -XX:+PrintGCDateStamps -XX:+PrintGCTimeStamps -XX:+UseGCLogFileRotation -XX:NumberOfGCLogFiles=10 -XX:GCLogFileSize=100M -Dloader.path=/home/<USER>/plugins,/home/<USER>/plugins/health,/home/<USER>/plugins/cmdb,/home/<USER>/plugins/selector -Dnacos.home=/home/<USER>/home/<USER>/target/nacos-server.jar  --spring.config.additional-location=file:/home/<USER>/conf/'
+ JAVA_OPT=' -XX:+UseConcMarkSweepGC -XX:+UseCMSCompactAtFullCollection -XX:CMSInitiatingOccupancyFraction=70 -XX:+CMSParallelRemarkEnabled -XX:SoftRefLRUPolicyMSPerMB=0 -XX:+CMSClassUnloadingEnabled -XX:SurvivorRatio=8  -Xms1g -Xmx1g -Xmn512m -Dnacos.standalone=true -Dnacos.core.auth.enabled=true -Dnacos.member.list= -Xloggc:/home/<USER>/logs/nacos_gc.log -verbose:gc -XX:+PrintGCDetails -XX:+PrintGCDateStamps -XX:+PrintGCTimeStamps -XX:+UseGCLogFileRotation -XX:NumberOfGCLogFiles=10 -XX:GCLogFileSize=100M -Dloader.path=/home/<USER>/plugins,/home/<USER>/plugins/health,/home/<USER>/plugins/cmdb,/home/<USER>/plugins/selector -Dnacos.home=/home/<USER>/home/<USER>/target/nacos-server.jar  --spring.config.additional-location=file:/home/<USER>/conf/ --spring.config.name=application'
+ JAVA_OPT=' -XX:+UseConcMarkSweepGC -XX:+UseCMSCompactAtFullCollection -XX:CMSInitiatingOccupancyFraction=70 -XX:+CMSParallelRemarkEnabled -XX:SoftRefLRUPolicyMSPerMB=0 -XX:+CMSClassUnloadingEnabled -XX:SurvivorRatio=8  -Xms1g -Xmx1g -Xmn512m -Dnacos.standalone=true -Dnacos.core.auth.enabled=true -Dnacos.member.list= -Xloggc:/home/<USER>/logs/nacos_gc.log -verbose:gc -XX:+PrintGCDetails -XX:+PrintGCDateStamps -XX:+PrintGCTimeStamps -XX:+UseGCLogFileRotation -XX:NumberOfGCLogFiles=10 -XX:GCLogFileSize=100M -Dloader.path=/home/<USER>/plugins,/home/<USER>/plugins/health,/home/<USER>/plugins/cmdb,/home/<USER>/plugins/selector -Dnacos.home=/home/<USER>/home/<USER>/target/nacos-server.jar  --spring.config.additional-location=file:/home/<USER>/conf/ --spring.config.name=application --logging.config=/home/<USER>/conf/nacos-logback.xml'
+ JAVA_OPT=' -XX:+UseConcMarkSweepGC -XX:+UseCMSCompactAtFullCollection -XX:CMSInitiatingOccupancyFraction=70 -XX:+CMSParallelRemarkEnabled -XX:SoftRefLRUPolicyMSPerMB=0 -XX:+CMSClassUnloadingEnabled -XX:SurvivorRatio=8  -Xms1g -Xmx1g -Xmn512m -Dnacos.standalone=true -Dnacos.core.auth.enabled=true -Dnacos.member.list= -Xloggc:/home/<USER>/logs/nacos_gc.log -verbose:gc -XX:+PrintGCDetails -XX:+PrintGCDateStamps -XX:+PrintGCTimeStamps -XX:+UseGCLogFileRotation -XX:NumberOfGCLogFiles=10 -XX:GCLogFileSize=100M -Dloader.path=/home/<USER>/plugins,/home/<USER>/plugins/health,/home/<USER>/plugins/cmdb,/home/<USER>/plugins/selector -Dnacos.home=/home/<USER>/home/<USER>/target/nacos-server.jar  --spring.config.additional-location=file:/home/<USER>/conf/ --spring.config.name=application --logging.config=/home/<USER>/conf/nacos-logback.xml --server.max-http-header-size=524288'
+ echo 'Nacos is starting, you can docker logs your container'
+ exec /usr/lib/jvm/java-1.8-openjdk/bin/java -XX:+UseConcMarkSweepGC -XX:+UseCMSCompactAtFullCollection '-XX:CMSInitiatingOccupancyFraction=70' -XX:+CMSParallelRemarkEnabled '-XX:SoftRefLRUPolicyMSPerMB=0' -XX:+CMSClassUnloadingEnabled '-XX:SurvivorRatio=8' -Xms1g -Xmx1g -Xmn512m '-Dnacos.standalone=true' '-Dnacos.core.auth.enabled=true' '-Dnacos.member.list=' -Xloggc:/home/<USER>/logs/nacos_gc.log -verbose:gc -XX:+PrintGCDetails -XX:+PrintGCDateStamps -XX:+PrintGCTimeStamps -XX:+UseGCLogFileRotation '-XX:NumberOfGCLogFiles=10' '-XX:GCLogFileSize=100M' '-Dloader.path=/home/<USER>/plugins,/home/<USER>/plugins/health,/home/<USER>/plugins/cmdb,/home/<USER>/plugins/selector' '-Dnacos.home=/home/<USER>' -jar /home/<USER>/target/nacos-server.jar '--spring.config.additional-location=file:/home/<USER>/conf/' '--spring.config.name=application' '--logging.config=/home/<USER>/conf/nacos-logback.xml' '--server.max-http-header-size=524288'
Nacos is starting, you can docker logs your container
OpenJDK 64-Bit Server VM warning: UseCMSCompactAtFullCollection is deprecated and will likely be removed in a future release.

         ,--.
       ,--.'|
   ,--,:  : |                                           Nacos 2.5.0
,`--.'`|  ' :                       ,---.               Running in stand alone mode, All function modules
|   :  :  | |                      '   ,'\   .--.--.    Port: 8848
:   |   \ | :  ,--.--.     ,---.  /   /   | /  /    '   Pid: 1
|   : '  '; | /       \   /     \.   ; ,. :|  :  /`./   Console: http://192.168.91.128:8848/nacos/index.html
'   ' ;.    ;.--.  .-. | /    / ''   | |: :|  :  ;_
|   | | \   | \__\/: . ..    ' / '   | .; : \  \    `.      https://nacos.io
'   : |  ; .' ," .--.; |'   ; :__|   :    |  `----.   \
|   | '`--'  /  /  ,.  |'   | '.'|\   \  /  /  /`--'  /
'   : |     ;  :   .'   \   :    : `----'  '--'.     /
;   |.'     |  ,     .-./\   \  /            `--'---'
'---'        `--`---'     `----'

2025-07-30 14:00:26,950 INFO Tomcat initialized with port(s): 8848 (http)

2025-07-30 14:00:27,250 INFO Root WebApplicationContext: initialization completed in 10712 ms

2025-07-30 14:00:41,258 INFO Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@26a262d6, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@11bd803, org.springframework.security.web.context.SecurityContextPersistenceFilter@51351f28, org.springframework.security.web.header.HeaderWriterFilter@724c5cbe, org.springframework.security.web.csrf.CsrfFilter@680d4a6a, org.springframework.security.web.authentication.logout.LogoutFilter@f1a45f8, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@15f2eda3, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@4d7aaca2, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@58f07f02, org.springframework.security.web.session.SessionManagementFilter@70e889e9, org.springframework.security.web.access.ExceptionTranslationFilter@309028af]

2025-07-30 14:00:42,333 INFO Adding welcome page: class path resource [static/index.html]

2025-07-30 14:00:44,038 INFO Exposing 14 endpoint(s) beneath base path '/actuator'

2025-07-30 14:00:44,120 WARN You are asking Spring Security to ignore Ant [pattern='/**']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.

2025-07-30 14:00:44,121 INFO Will not secure Ant [pattern='/**']

2025-07-30 14:00:44,121 WARN You are asking Spring Security to ignore Mvc [pattern='/prometheus']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.

2025-07-30 14:00:44,121 INFO Will not secure Mvc [pattern='/prometheus']

2025-07-30 14:00:44,122 WARN You are asking Spring Security to ignore Mvc [pattern='/prometheus/namespaceId/{namespaceId}']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.

2025-07-30 14:00:44,122 INFO Will not secure Mvc [pattern='/prometheus/namespaceId/{namespaceId}']

2025-07-30 14:00:44,122 WARN You are asking Spring Security to ignore Mvc [pattern='/prometheus/namespaceId/{namespaceId}/service/{service}']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.

2025-07-30 14:00:44,122 INFO Will not secure Mvc [pattern='/prometheus/namespaceId/{namespaceId}/service/{service}']

2025-07-30 14:00:44,324 INFO Tomcat started on port(s): 8848 (http) with context path '/nacos'

2025-07-30 14:00:44,341 INFO No TaskScheduler/ScheduledExecutorService bean found for scheduled processing

2025-07-30 14:00:44,432 INFO Nacos started successfully in stand alone mode. use external storage

2025-07-30 14:00:45,629 INFO Initializing Servlet 'dispatcherServlet'

2025-07-30 14:00:45,631 INFO Completed initialization in 2 ms

[root@192 ~]# cat /docker/env/nacos/conf/application.properties | grep auth
nacos.core.auth.enabled=false
nacos.core.auth.server.identity.key=serverIdentity
nacos.core.auth.server.identity.value=security
nacos.core.auth.plugin.nacos.token.secret.key=SecretKey012345678901234567890123456789012345678901234567890123456789
[root@192 ~]# grep "NACOS_AUTH_ENABLE" /path/to/your/project/scripts/config.sh
grep: /path/to/your/project/scripts/config.sh: No such file or directory
[root@192 ~]# telnet ************* 8848
bash: telnet: command not found...
[root@192 ~]#
